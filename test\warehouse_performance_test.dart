import 'package:flutter_test/flutter_test.dart';
import 'package:mobile_pos/Screens/Warehouse/warehouse_model.dart';
import 'package:mobile_pos/model/product_model.dart';

void main() {
  group('اختبارات أداء المخازن', () {
    
    // إنشاء بيانات اختبار
    List<WareHouseModel> createTestWarehouses(int count) {
      return List.generate(count, (index) => WareHouseModel(
        id: 'warehouse_$index',
        warehouseName: 'مخزن $index',
        warehouseAddress: 'عنوان $index',
      ));
    }

    List<ProductModel> createTestProducts(int count, List<WareHouseModel> warehouses) {
      return List.generate(count, (index) {
        final warehouseIndex = index % warehouses.length;
        return ProductModel(
          productId: 'product_$index',
          productName: 'منتج $index',
          productStock: '${(index % 100) + 1}', // مخزون من 1 إلى 100
          productSalePrice: '${((index % 50) + 1) * 10}', // سعر من 10 إلى 500
          warehouseId: warehouses[warehouseIndex].id,
          productCode: 'CODE_$index',
          productCategory: 'فئة ${index % 10}',
          productBrand: 'علامة ${index % 5}',
          productUnit: 'قطعة',
          productPurchasePrice: '${((index % 40) + 1) * 8}',
          productDiscount: '0',
          productWholeSalePrice: '${((index % 45) + 1) * 9}',
          productDealerPrice: '${((index % 35) + 1) * 7}',
          productManufacturer: 'مصنع ${index % 3}',
          productPicture: '',
          size: 'متوسط',
          color: 'أبيض',
          weight: '1',
          capacity: '1',
          type: 'عادي',
          warranty: '1 سنة',
          serialNumber: 'SN_$index',
          nsnNumber: '',
          brandName: 'علامة ${index % 5}',
          categoryName: 'فئة ${index % 10}',
          unitName: 'قطعة',
          lowerStockAlert: '5',
          excpiryDate: '',
          manufacturingDate: '',
          barcode: 'BARCODE_$index',
        );
      });
    }

    test('اختبار الأداء مع بيانات صغيرة (10 مخازن، 100 منتج)', () {
      final stopwatch = Stopwatch()..start();
      
      final warehouses = createTestWarehouses(10);
      final products = createTestProducts(100, warehouses);
      
      // محاكاة حساب البيانات
      Map<String, List<ProductModel>> productsByWarehouse = {};
      for (var product in products) {
        if (product.warehouseId.isNotEmpty) {
          productsByWarehouse.putIfAbsent(product.warehouseId, () => []);
          productsByWarehouse[product.warehouseId]!.add(product);
        }
      }

      double grandTotal = 0;
      for (var warehouse in warehouses) {
        final warehouseProducts = productsByWarehouse[warehouse.id] ?? [];
        for (var product in warehouseProducts) {
          double productStock = double.tryParse(product.productStock) ?? 0;
          double productPrice = double.tryParse(product.productSalePrice) ?? 0;
          grandTotal += productStock * productPrice;
        }
      }
      
      stopwatch.stop();
      
      expect(grandTotal, greaterThan(0));
      expect(stopwatch.elapsedMilliseconds, lessThan(100)); // يجب أن يكون أقل من 100ms
      
      print('وقت التنفيذ للبيانات الصغيرة: ${stopwatch.elapsedMilliseconds}ms');
      print('الإجمالي المحسوب: $grandTotal');
    });

    test('اختبار الأداء مع بيانات متوسطة (50 مخزن، 1000 منتج)', () {
      final stopwatch = Stopwatch()..start();
      
      final warehouses = createTestWarehouses(50);
      final products = createTestProducts(1000, warehouses);
      
      // محاكاة حساب البيانات
      Map<String, List<ProductModel>> productsByWarehouse = {};
      for (var product in products) {
        if (product.warehouseId.isNotEmpty) {
          productsByWarehouse.putIfAbsent(product.warehouseId, () => []);
          productsByWarehouse[product.warehouseId]!.add(product);
        }
      }

      double grandTotal = 0;
      for (var warehouse in warehouses) {
        final warehouseProducts = productsByWarehouse[warehouse.id] ?? [];
        for (var product in warehouseProducts) {
          double productStock = double.tryParse(product.productStock) ?? 0;
          double productPrice = double.tryParse(product.productSalePrice) ?? 0;
          grandTotal += productStock * productPrice;
        }
      }
      
      stopwatch.stop();
      
      expect(grandTotal, greaterThan(0));
      expect(stopwatch.elapsedMilliseconds, lessThan(500)); // يجب أن يكون أقل من 500ms
      
      print('وقت التنفيذ للبيانات المتوسطة: ${stopwatch.elapsedMilliseconds}ms');
      print('الإجمالي المحسوب: $grandTotal');
    });

    test('اختبار الأداء مع بيانات كبيرة (100 مخزن، 5000 منتج)', () {
      final stopwatch = Stopwatch()..start();
      
      final warehouses = createTestWarehouses(100);
      final products = createTestProducts(5000, warehouses);
      
      // محاكاة حساب البيانات
      Map<String, List<ProductModel>> productsByWarehouse = {};
      for (var product in products) {
        if (product.warehouseId.isNotEmpty) {
          productsByWarehouse.putIfAbsent(product.warehouseId, () => []);
          productsByWarehouse[product.warehouseId]!.add(product);
        }
      }

      double grandTotal = 0;
      for (var warehouse in warehouses) {
        final warehouseProducts = productsByWarehouse[warehouse.id] ?? [];
        for (var product in warehouseProducts) {
          double productStock = double.tryParse(product.productStock) ?? 0;
          double productPrice = double.tryParse(product.productSalePrice) ?? 0;
          grandTotal += productStock * productPrice;
        }
      }
      
      stopwatch.stop();
      
      expect(grandTotal, greaterThan(0));
      expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // يجب أن يكون أقل من 2 ثانية
      
      print('وقت التنفيذ للبيانات الكبيرة: ${stopwatch.elapsedMilliseconds}ms');
      print('الإجمالي المحسوب: $grandTotal');
    });

    test('اختبار معالجة البيانات الخاطئة', () {
      final warehouses = createTestWarehouses(5);
      final products = <ProductModel>[
        ProductModel(
          productId: 'product_1',
          productName: 'منتج 1',
          productStock: 'invalid', // قيمة خاطئة
          productSalePrice: '100',
          warehouseId: warehouses[0].id,
          productCode: 'CODE_1',
          productCategory: 'فئة 1',
          productBrand: 'علامة 1',
          productUnit: 'قطعة',
          productPurchasePrice: '80',
          productDiscount: '0',
          productWholeSalePrice: '90',
          productDealerPrice: '70',
          productManufacturer: 'مصنع 1',
          productPicture: '',
          size: 'متوسط',
          color: 'أبيض',
          weight: '1',
          capacity: '1',
          type: 'عادي',
          warranty: '1 سنة',
          serialNumber: 'SN_1',
          nsnNumber: '',
          brandName: 'علامة 1',
          categoryName: 'فئة 1',
          unitName: 'قطعة',
          lowerStockAlert: '5',
          excpiryDate: '',
          manufacturingDate: '',
          barcode: 'BARCODE_1',
        ),
      ];
      
      // محاكاة حساب البيانات مع معالجة الأخطاء
      Map<String, List<ProductModel>> productsByWarehouse = {};
      for (var product in products) {
        if (product.warehouseId.isNotEmpty) {
          productsByWarehouse.putIfAbsent(product.warehouseId, () => []);
          productsByWarehouse[product.warehouseId]!.add(product);
        }
      }

      double grandTotal = 0;
      for (var warehouse in warehouses) {
        final warehouseProducts = productsByWarehouse[warehouse.id] ?? [];
        for (var product in warehouseProducts) {
          double productStock = double.tryParse(product.productStock) ?? 0; // يجب أن يرجع 0
          double productPrice = double.tryParse(product.productSalePrice) ?? 0;
          if (productStock.isFinite && productPrice.isFinite) {
            grandTotal += productStock * productPrice;
          }
        }
      }
      
      expect(grandTotal, equals(0)); // يجب أن يكون 0 بسبب القيمة الخاطئة
      print('تم التعامل مع البيانات الخاطئة بنجاح');
    });
  });
}
